package fbg.parser;

import fbg.configuration.HwcConfiguration;
import fbg.configuration.ImwcConfiguration;
import fbg.configuration.OmsConfiguration;
import fbg.configuration.OsimcConfiguration;
import fbg.configuration.PlbcConfiguration;
import fbg.configuration.TbcConfiguration;
import fbg.parser.hermes.HermesOrderExportParser;
import fbg.parser.hermes.HermesOrderReturnParser;
import fbg.parser.hermes.HermesOrderUpdateParser;
import fbg.parser.ingrammicro.returns.IngramMicroOrderReturnParser;
import fbg.parser.plbc.ParcelLabPayloadsParser;
import fbg.parser.sfcc.SfccOrderImportParser;
import fbg.parser.sfcc.SfccOrderStatusExportParser;
import fbg.parser.tradebyte.TradebyteOrderImportParser;
import fbg.parser.tradebyte.TradebyteOrderStatusExportParser;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Optional;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ArchiveParserFactory}.
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({System.class, LoggerFactory.class, OmsConfiguration.class, ImwcConfiguration.class, HwcConfiguration.class,
    TbcConfiguration.class, OsimcConfiguration.class, PlbcConfiguration.class})
public class ArchiveParserFactoryTest {

    private static final String OMS_BUCKET_NAME = "omsBucketName";
    private static final String HWC_BUCKET_NAME = "hwcBucketName";
    private static final String IMWC_BUCKET_NAME = "imwcBucketName";
    private static final String TBC_BUCKET_NAME = "tbcBucketName";
    private static final String OSIMC_BUCKET_NAME = "osimcBucketName";
    private static final String PLBC_BUCKET_NAME = "plbcBucketName";

    private ArchiveParserFactory archiveParserFactory;

    /**
     * Setup environment variables.
     */
    @BeforeClass
    public static void setUpClass() {
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.getenv("S3_OMS_BUCKET_NAME")).thenReturn(OMS_BUCKET_NAME);
        when(OmsConfiguration.getInstance().getBucketName()).thenReturn(OMS_BUCKET_NAME);
        PowerMockito.when(System.getenv("S3_HWC_BUCKET_NAME")).thenReturn(HWC_BUCKET_NAME);
        when(HwcConfiguration.getInstance().getBucketName()).thenReturn(HWC_BUCKET_NAME);
        PowerMockito.when(System.getenv("S3_IMWC_BUCKET_NAME")).thenReturn(IMWC_BUCKET_NAME);
        when(ImwcConfiguration.getInstance().getBucketName()).thenReturn(IMWC_BUCKET_NAME);
        PowerMockito.when(System.getenv("S3_TBC_BUCKET_NAME")).thenReturn(TBC_BUCKET_NAME);
        when(TbcConfiguration.getInstance().getBucketName()).thenReturn(TBC_BUCKET_NAME);
        PowerMockito.when(System.getenv("S3_OSIMC_BUCKET_NAME")).thenReturn(OSIMC_BUCKET_NAME);
        when(OsimcConfiguration.getInstance().getBucketName()).thenReturn(OSIMC_BUCKET_NAME);
        PowerMockito.when(System.getenv("S3_PLBC_BUCKET_NAME")).thenReturn(PLBC_BUCKET_NAME);
        when(PlbcConfiguration.getInstance().getBucketName()).thenReturn(PLBC_BUCKET_NAME);
    }

    @Before
    public void setUp() {
        Logger logger = Mockito.mock(Logger.class);
        PowerMockito.mockStatic(LoggerFactory.class);
        Mockito.when(LoggerFactory.getLogger(Mockito.any(Class.class))).thenReturn(logger);

        archiveParserFactory = new ArchiveParserFactory();
    }

    @Test
    public void processS3Object_omsBucketNameAndSfccFolder_returnSfccOrderImportParser() {
        // act
        Optional result = archiveParserFactory.getParser(OMS_BUCKET_NAME, "sfcc-order/");

        // assert
        assertTrue("OMS bucket SFCC folder: Result is not an instance of SfccOrderImportParser",
            result.get() instanceof SfccOrderImportParser);
    }

    @Test
    public void processS3Object_tbcBucketNameAndTradebyteFolder_returnTradebyteOrderImportParser() {
        // act
        Optional result = archiveParserFactory.getParser(TBC_BUCKET_NAME, "tb-order/");

        // assert
        assertTrue("OMS bucket Tradebyte folder: Result is not an instance of TradebyteOrderImportParser",
            result.get() instanceof TradebyteOrderImportParser);
    }

    @Test(expected = ArchiveParseException.class)
    public void processS3Object_omsBucketNameAndWrongFolder_throwException() {
        // act
        archiveParserFactory.getParser(OMS_BUCKET_NAME, "wrong/");
    }

    @Test
    public void processS3Object_hwcBucketNameAndFaktFolder_returnHermesOrderUpdateParser() {
        // act
        Optional result = archiveParserFactory.getParser(HWC_BUCKET_NAME, "fakt/");

        // assert
        assertTrue("HWC bucket FAKT folder: Result is not an instance of HermesOrderUpdateParser",
            result.get() instanceof HermesOrderUpdateParser);
    }

    @Test
    public void processS3Object_hwcBucketNameAndPodFolder_returnHermesOrderUpdateParser() {
        // act
        Optional result = archiveParserFactory.getParser(HWC_BUCKET_NAME, "pod/");

        // assert
        assertTrue("HWC bucket POD folder: Result is not an instance of HermesOrderUpdateParser",
            result.get() instanceof HermesOrderUpdateParser);
    }

    @Test
    public void processS3Object_hwcBucketNameAndReturnsFolder_returnHermesOrderReturnParser() {
        // act
        Optional result = archiveParserFactory.getParser(HWC_BUCKET_NAME, "returns/");

        // assert
        assertTrue("HWC bucket Returns folder: Result is not an instance of HermesOrderReturnParser",
            result.get() instanceof HermesOrderReturnParser);
    }

    @Test(expected = ArchiveParseException.class)
    public void processS3Object_hwcBucketNameAndWrongFolder_throwException() {
        // act
        archiveParserFactory.getParser(HWC_BUCKET_NAME, "wrong/");
    }

    @Test
    public void processS3Object_imwcBucketNameAndFaktPrefix_returnHermesOrderUpdateParser() {
        // act
        Optional result = archiveParserFactory.getParser(IMWC_BUCKET_NAME, "t_orderstat_fakt");

        // assert
        assertTrue("IMWC bucket FAKT folder: Result is not an instance of HermesOrderUpdateParser",
            result.get() instanceof HermesOrderUpdateParser);
    }

    @Test
    public void processS3Object_imwcBucketNameAndPodPrefix_returnHermesOrderUpdateParser() {
        // act
        Optional result = archiveParserFactory.getParser(IMWC_BUCKET_NAME, "t_orderstat_pod");

        // assert
        assertTrue("IMWC bucket POD folder: Result is not an instance of HermesOrderUpdateParser",
            result.get() instanceof HermesOrderUpdateParser);
    }

    @Test
    public void processS3Object_imwcBucketNameAndReturnsPrefix_returnHermesOrderReturnParser() {
        // act
        Optional result = archiveParserFactory.getParser(IMWC_BUCKET_NAME, "t_returnsum");

        // assert
        assertTrue("IMWC bucket Return folder: Result is not an instance of HermesOrderReturnParser",
            result.get() instanceof HermesOrderReturnParser);
    }

    @Test
    public void processS3Object_imwcBucketWrongPrefix_returnEmpty() {
        // act
        Optional result = archiveParserFactory.getParser(IMWC_BUCKET_NAME, "wrong");

        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void processS3Object_hwcBucketNameAndOrderExportFolder_returnHermesOrderImportParser() {
        // act
        Optional result = archiveParserFactory.getParser(HWC_BUCKET_NAME, "export/");

        // assert
        assertTrue("HWC bucket export folder: Result is not an instance of HermesOrderExportParser",
            result.get() instanceof HermesOrderExportParser);
    }

    @Test
    public void processS3Object_imwcBucketNameAndOrderExportFolder_returnHermesOrderImportParser() {
        // act
        Optional result = archiveParserFactory.getParser(IMWC_BUCKET_NAME, "Order_Import_40_");

        // assert
        assertTrue("IMWC bucket export folder: Result is not an instance of HermesOrderExportParser",
            result.get() instanceof HermesOrderExportParser);
    }

    @Test
    public void processS3Object_omsBucketNameAndSfccOrderStatusFolder_returnSfccOrderStatusParser() {
        // act
        Optional result = archiveParserFactory.getParser(OMS_BUCKET_NAME, "sfcc-order-status/");

        // assert
        assertTrue("OMS bucket sfcc order status folder: Result is not an instance of SfccOrderStatusExportParser",
            result.get() instanceof SfccOrderStatusExportParser);
    }

    @Test
    public void processS3Object_omsBucketNameAndTradebyteOrderStatusFolder_returnTradebyteOrderStatusParser() {
        // act
        Optional result = archiveParserFactory.getParser(OMS_BUCKET_NAME, "tb-order-status/");

        // assert
        assertTrue("OMS bucket tb order status folder: Result is not an instance of TradebyteOrderStatusExportParser",
            result.get() instanceof TradebyteOrderStatusExportParser);
    }

    @Test
    public void processS3Object_osimcBucketNameAndReturnsPrefix_returnIngramMicroReturnsParser() {
        // act
        Optional result = archiveParserFactory.getParser(OSIMC_BUCKET_NAME, "returns");

        // assert
        assertTrue("OSIMC bucket Return folder: Result is not an instance of IngramMicroOrderReturnParser",
            result.get() instanceof IngramMicroOrderReturnParser);
    }

    @Test
    public void processS3Object_plbcBucketNameAndReturnsPrefix_returnParcelLabPayloadReturnsParser() {
        // act
        Optional result = archiveParserFactory.getParser(PLBC_BUCKET_NAME, "payloads");

        // assert
        assertTrue("PLBC bucket Payloads folder: Result is not an instance of ParcelLabPayloadsParser",
            result.get() instanceof ParcelLabPayloadsParser);
    }
}
