package fbg.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import fbg.configuration.ElasticSearchConfiguration;
import fbg.generated.elasticsearch.model.ArchiveContent;
import fbg.generated.elasticsearch.model.CountsApiResponse;
import fbg.generated.elasticsearch.model.Hit;
import fbg.generated.elasticsearch.model.Hits;
import fbg.generated.elasticsearch.model.SearchArchiveContentResponse;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.any;

/**
 * Unit test for {@link ElasticSearchService}.
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClients.class, LoggerFactory.class})
public class ElasticSearchServiceTest {

    private static final String TEST_LOCAL_URI = "https://lambda.com/files/file/";
    private static final String ORDER_ID = "orderId";
    private static Logger logger;

    private ArchiveContent archiveContent;
    private StatusLine statusLine;
    private CloseableHttpClient httpClient;
    private ElasticSearchService elasticSearchService;
    private CloseableHttpResponse response;

    @Before
    public void setUp() throws IOException {
        ReflectionTestUtils.setField(ElasticSearchConfiguration.getInstance(), "endpoint", "https://lambda.com");
        PowerMockito.mockStatic(LoggerFactory.class);
        httpClient = mock(CloseableHttpClient.class);
        PowerMockito.mockStatic(HttpClients.class);
        PowerMockito.when(HttpClients.createDefault()).thenReturn(httpClient);
        logger = Mockito.mock(Logger.class);
        PowerMockito.mockStatic(LoggerFactory.class);
        Mockito.when(LoggerFactory.getLogger(Mockito.any(Class.class))).thenReturn(logger);

        archiveContent = new ArchiveContent();
        archiveContent.setS3bucket("S3_BUCKET");
        archiveContent.setType("TYPE");
        archiveContent.setFilePath("FILENAME");
        response = mock(CloseableHttpResponse.class);
        statusLine = mock(StatusLine.class);
        when(response.getStatusLine()).thenReturn(statusLine);
        when(httpClient.execute(any(HttpRequestBase.class))).thenReturn(response);

        elasticSearchService = new ElasticSearchService();
    }

    @Test
    public void putDocument_archiveContentCreated_returnTrue() throws Exception {
        // arrange
        when(statusLine.getStatusCode()).thenReturn(HttpStatus.SC_CREATED);

        // act
        boolean result = elasticSearchService.putOrderDocument(archiveContent);

        // assert
        assertTrue("ArchiveContentCreated: Document should be uploaded successfully", result);
        ArgumentCaptor<HttpPost> captor = ArgumentCaptor.forClass(HttpPost.class);
        verify(httpClient).execute(captor.capture());
        assertEquals("ArchiveContentCreated: Constructed URI and the test URI are mismatch",
                TEST_LOCAL_URI, captor.getValue().getURI().toString());
        assertEquals("ArchiveContentCreated: Request Content-type header is not application/json ",
                ContentType.APPLICATION_JSON.getMimeType(), (captor.getValue().getHeaders(HttpHeaders.CONTENT_TYPE))[0].getValue());
    }

    @Test
    public void putDocument_archiveContentUpdated_returnTrue() throws Exception {
        // arrange
        when(statusLine.getStatusCode()).thenReturn(HttpStatus.SC_OK);

        // act
        boolean result = elasticSearchService.putOrderDocument(archiveContent);

        // assert
        assertTrue("ArchiveContentUpdated: Document should be uploaded successfully", result);
        ArgumentCaptor<HttpPost> captor = ArgumentCaptor.forClass(HttpPost.class);
        verify(httpClient).execute(captor.capture());
        assertEquals("ArchiveContentUpdated: Constructed URI and the test URI are mismatch",
                TEST_LOCAL_URI, captor.getValue().getURI().toString());
        assertEquals("ArchiveContentUpdated: Request Content-type header is not application/json ",
                ContentType.APPLICATION_JSON.getMimeType(), (captor.getValue().getHeaders(HttpHeaders.CONTENT_TYPE))[0].getValue());
    }

    @Test
    public void putDocument_archiveContentFailed_returnFalse() throws IOException {
        // arrange
        when(statusLine.getStatusCode()).thenReturn(HttpStatus.SC_BAD_REQUEST);

        // act
        boolean result = elasticSearchService.putOrderDocument(archiveContent);

        // assert
        assertFalse("ArchiveContentFailed: Document shouldn't be uploaded successfully", result);
        ArgumentCaptor<HttpPost> captor = ArgumentCaptor.forClass(HttpPost.class);
        verify(httpClient).execute(captor.capture());
        assertEquals("ArchiveContentFailed: Constructed URI and the test URI are mismatch",
                TEST_LOCAL_URI, captor.getValue().getURI().toString());
        assertEquals("ArchiveContentFailed: Request Content-type header is not application/json ",
                ContentType.APPLICATION_JSON.getMimeType(), (captor.getValue().getHeaders(HttpHeaders.CONTENT_TYPE))[0].getValue());
    }

    @Test(expected = ElasticSearchException.class)
    public void putDocument_archiveContentFailedDueToIOException_elasticSearchExceptionThrown() throws IOException {
        // arrange
        when(httpClient.execute(any())).thenThrow(new IOException());

        // act
        elasticSearchService.putOrderDocument(archiveContent);
    }

    @Test
    public void getOrderDocuments_documentsRequested_documentsReceived() throws IOException {
        // arrange
        ObjectMapper objectMapper = new ObjectMapper()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .registerModule(new JavaTimeModule());
        HttpEntity httpEntity = mock(HttpEntity.class);

        CountsApiResponse countsApiResponse =
                new CountsApiResponse().withCount(1);

        SearchArchiveContentResponse contentResponse =
                new SearchArchiveContentResponse()
                        .withHits(new Hits().withHits(Collections.singletonList(new Hit().withSource(archiveContent)))
                                .withTotal(1));

        when(httpEntity.getContent())
                .thenReturn(IOUtils.toInputStream(objectMapper.writeValueAsString(countsApiResponse), StandardCharsets.UTF_8))
                .thenReturn(IOUtils.toInputStream(objectMapper.writeValueAsString(contentResponse), StandardCharsets.UTF_8));
        when(response.getEntity()).thenReturn(httpEntity);
        when(statusLine.getStatusCode()).thenReturn(HttpStatus.SC_OK);

        // act
        List<ArchiveContent> actualArchiveContents = elasticSearchService.getOrderDocuments(ORDER_ID);

        // assert
        assertEquals("Bucket name mismatch", archiveContent.getS3bucket(), actualArchiveContents.get(0).getS3bucket());
        assertEquals("File path mismatch", archiveContent.getFilePath(), actualArchiveContents.get(0).getFilePath());
        assertEquals("Order list mismatch", archiveContent.getOrders(), actualArchiveContents.get(0).getOrders());

    }

    @Test
    public void getOrderDocuments_documentNotFound_emptyListReceived() throws IOException {
        // arrange
        ObjectMapper objectMapper = new ObjectMapper()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .registerModule(new JavaTimeModule());
        HttpEntity httpEntity = mock(HttpEntity.class);
        CountsApiResponse countsApiResponse =
                new CountsApiResponse().withCount(1);
        when(response.getEntity()).thenReturn(httpEntity);
        when(httpEntity.getContent())
                .thenReturn(IOUtils.toInputStream(objectMapper.writeValueAsString(countsApiResponse), StandardCharsets.UTF_8));

        when(statusLine.getStatusCode()).thenReturn(HttpStatus.SC_OK).thenReturn(HttpStatus.SC_NOT_FOUND);

        // act
        List<ArchiveContent> actualArchiveContents = elasticSearchService.getOrderDocuments(ORDER_ID);

        // assert
        assertTrue(actualArchiveContents.isEmpty());
    }

    @Test(expected = ElasticSearchException.class)
    public void getOrderDocuments_handlingException_elasticSearchExceptionThrown() throws IOException {
        // arrange
        when(httpClient.execute(any(HttpRequestBase.class))).thenThrow(IOException.class);

        // act
        elasticSearchService.getOrderDocuments(ORDER_ID);
    }

    @Test
    public void getOrderDocuments_countsFails_elasticSearchExceptionThrown() {
        // arrange
        when(response.getStatusLine().getStatusCode()).thenReturn(HttpStatus.SC_NOT_FOUND);
        // act
        try {
            elasticSearchService.getOrderDocuments(ORDER_ID);
        } catch (ElasticSearchException ex) {
            assertTrue(ex.getMessage().contains(((Integer) HttpStatus.SC_NOT_FOUND).toString()));
        }
    }

}
