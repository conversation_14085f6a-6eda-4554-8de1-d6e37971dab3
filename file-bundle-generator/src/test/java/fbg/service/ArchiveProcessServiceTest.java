package fbg.service;

import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3Object;
import fbg.configuration.FileType;
import fbg.context.BeanFactory;
import fbg.generated.elasticsearch.model.ArchiveContent;
import fbg.parser.ArchiveParser;
import fbg.parser.ArchiveParserFactory;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ArchiveProcessService}.
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({LoggerFactory.class, BeanFactory.class})
public class ArchiveProcessServiceTest {
    private static Logger logger;
    private static BeanFactory beanFactory;

    private ArchiveParserFactory archiveParserFactory;
    private ArchiveProcessService archiveProcessService;

    /**
     * Setup environment variables.
     */
    @BeforeClass
    public static void setUpClass() {
        logger = mock(Logger.class);
        PowerMockito.mockStatic(LoggerFactory.class);
        PowerMockito.when(LoggerFactory.getLogger(any(Class.class))).thenReturn(logger);

        beanFactory = mock(BeanFactory.class);
        Whitebox.setInternalState(BeanFactory.class, "instance", beanFactory);
    }

    @Before
    public void setUp() {
        archiveParserFactory = mock(ArchiveParserFactory.class);
        when(beanFactory.getArchiveParserFactory()).thenReturn(archiveParserFactory);
        archiveProcessService = new ArchiveProcessService();
    }

    @Test
    public void processS3Object_s3Object_callAmazonS3() {

        // arrange
        ArchiveParser archiveParser = mock(ArchiveParser.class);
        when(archiveParser.getArchiveType()).thenReturn(FileType.ORDER);
        when(archiveParser.parse(any(S3Object.class))).thenReturn(new HashSet<>(Collections.singletonList("ORDER_1")));
        when(archiveParserFactory.getParser(anyString(), anyString())).thenReturn(Optional.of(archiveParser));
        S3Object s3Object = new S3Object();
        s3Object.setKey("localhost://files/filename");
        s3Object.setBucketName("bucketName");
        Date lastModified = new Date();
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setLastModified(lastModified);
        s3Object.setObjectMetadata(objectMetadata);
        ArchiveContent archiveContent = new ArchiveContent();
        archiveContent.setFilePath("localhost://files/filename");
        archiveContent.setS3bucket("bucketName");
        archiveContent.withType(FileType.ORDER.name()).withOrders(Collections.singletonList("ORDER_1"))
                .withLastModified(ZonedDateTime.ofInstant(lastModified.toInstant(),
                        ZoneOffset.UTC));

        // act
        ArchiveContent result = archiveProcessService.processS3Object(s3Object);

        // assert
        assertEquals(archiveContent.getType(), result.getType());
        assertEquals(archiveContent.getFilePath(), result.getFilePath());
        assertEquals(1, result.getOrders().size());
    }

    @Test
    public void processS3Object_parserNotFound_log() {

        // arrange
        when(archiveParserFactory.getParser(anyString(), anyString())).thenReturn(Optional.empty());
        S3Object s3Object = new S3Object();
        s3Object.setKey("files/filename");
        s3Object.setBucketName("bucketName");
        ArchiveContent archiveContent = new ArchiveContent();
        archiveContent.setFilePath("files/filename");
        archiveContent.setS3bucket("bucketName");

        // act
        ArchiveContent result = archiveProcessService.processS3Object(s3Object);

        // assert
        verify(logger).info(anyString(), anyString(), anyString());
        assertEquals(archiveContent.getType(), result.getType());
        assertEquals(archiveContent.getFilePath(), result.getFilePath());
        assertTrue(result.getOrders().isEmpty());

    }

}
