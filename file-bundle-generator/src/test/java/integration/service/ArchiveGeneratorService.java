package integration.service;

import integration.enums.ArchiveType;
import integration.enums.Bucket;
import integration.model.InMemoryArchive;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service used to generate test archives.
 */
public class ArchiveGeneratorService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ArchiveGeneratorService.class);

    private static Map<String, String> orderKeyMap;
    private static Map<String, InMemoryArchive> archiveMap;

    public void initKeyMaps() {
        orderKeyMap = new ConcurrentHashMap<>();
        archiveMap = new ConcurrentHashMap<>();
    }

    /**
     * Generate the content and the coordinates of an archive.
     */
    public InMemoryArchive generateInMemoryArchive(String fileNameKey, String bucketKey, String archiveTypeKey, String[] orderIdKeys) {
        ArchiveType archiveType = ArchiveType.getArchiveType(archiveTypeKey);
        List<String> orderIds = generateOrGetOrderIds(orderIdKeys);
        InMemoryArchive inMemoryArchive = archiveType.generateArchive(orderIds);
        Bucket bucket = Bucket.getBucket(bucketKey);
        bucket.enrich(inMemoryArchive, archiveType);

        archiveMap.put(fileNameKey, inMemoryArchive);
        return inMemoryArchive;
    }

    private List<String> generateOrGetOrderIds(String[] orderIdKeys) {
        List<String> orderIds = new ArrayList<>();
        for (String orderIdKey : orderIdKeys) {
            String orderId = orderKeyMap.get(orderIdKey);
            if (orderId == null) {
                ArchiveType archiveType = null;
                if (orderIdKey.startsWith("cc")) {
                    archiveType = ArchiveType.CC_ORDER;
                } else if (orderIdKey.startsWith("tb")) {
                    archiveType = ArchiveType.TB_ORDER;
                }
                Objects.requireNonNull(archiveType, "Couldn't generate an order with key " + orderIdKey);
                final String nextOrderNumber = archiveType.getNextOrderNumber();
                orderKeyMap.putIfAbsent(orderIdKey, nextOrderNumber);
                orderId = orderKeyMap.get(orderIdKey);
            }
            orderIds.add(orderId);
        }
        return orderIds;
    }

    /**
     * Log the values of the order and archive keys.
     */
    public void logKeys() {
        for (Map.Entry<String, String> entry : orderKeyMap.entrySet()) {
            LOGGER.info(" order key: {} value: {} ", entry.getKey(), entry.getValue());
        }
        for (Map.Entry<String, InMemoryArchive> entry : archiveMap.entrySet()) {
            LOGGER.info(" archive key: {} value: {} ", entry.getKey(), entry.getValue().makeObjectKey());
        }

    }

    public String getOrderId(String orderIdKey) {
        return orderKeyMap.get(orderIdKey);
    }

    public InMemoryArchive getArchive(String archiveKey) {
        return archiveMap.get(archiveKey);
    }
}
