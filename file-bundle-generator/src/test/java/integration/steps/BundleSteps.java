package integration.steps;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import fbg.functions.BundleGenerationHandler;
import integration.FunctionalTestSession;
import integration.model.InMemoryArchive;
import integration.service.ArchiveGeneratorService;
import org.apache.commons.io.IOUtils;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.jbehave.core.annotations.Named;
import org.jbehave.core.annotations.Then;
import org.jbehave.core.annotations.When;
import org.jbehave.core.model.ExamplesTable;
import org.jbehave.core.steps.Parameters;
import org.junit.Assert;

import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * Bundle generation steps.
 */
public class BundleSteps {
    private static final Logger LOGGER = LoggerFactory.getLogger(BundleSteps.class);

    private ArchiveGeneratorService archiveGeneratorService = FunctionalTestSession.getInstance().getArchiveGeneratorService();

    private Map<String, String> actualContents = new HashMap<>();

    /**
     * Downloads and order bundle and saves the contents for later verification.
     */
    @When("order bundle is downloaded for order $orderIdKey")
    public void requestOrderBundle(@Named("orderIdKey") String orderIdKey) throws Exception {
        BundleGenerationHandler handler = new BundleGenerationHandler();
        APIGatewayProxyRequestEvent requestEvent = new APIGatewayProxyRequestEvent();
        requestEvent.setPathParameters(Collections.singletonMap("orderBundle", this.archiveGeneratorService.getOrderId(orderIdKey)));

        APIGatewayProxyResponseEvent responseEvent = handler.handleRequest(requestEvent, null);
        URL url = new URL(responseEvent.getBody());
        try (ZipInputStream zipInputStream = new ZipInputStream(url.openStream())) {
            ZipEntry zipEntry = zipInputStream.getNextEntry();

            while (zipEntry != null) {
                String name = zipEntry.getName();
                String content = IOUtils.toString(zipInputStream, StandardCharsets.UTF_8);

                this.actualContents.put(name, content);
                zipEntry = zipInputStream.getNextEntry();
            }
        }

        LOGGER.info(responseEvent.getBody());
    }

    /**
     * Verifies that the given fileKeyIds are in the bundle and have the right content.
     */
    @Then("order bundle contains the files with the following directory structure : $bundleContent")
    public void checkBundleContents(ExamplesTable bundleContent) {
        for (Parameters parameters : bundleContent.getRowsAsParameters()) {
            String expectedDirectoryInBundle = parameters.valueAs("directoryInBundle", String.class);
            List<String> expectedFileNameKeysInBundle = Arrays.asList(parameters.valueAs("fileNameKeys", String.class).toString()
                    .split(","));
            for (String expectedFileNameKeyInBundle : expectedFileNameKeysInBundle) {
                InMemoryArchive inMemoryArchive = this.archiveGeneratorService.getArchive(expectedFileNameKeyInBundle);

                String expectedFileContentInBundle = inMemoryArchive.getContent();
                String actualFileContentInBundle = this.actualContents.get(expectedDirectoryInBundle + "/" + inMemoryArchive.getFileName());

                Assert.assertEquals(expectedFileContentInBundle, actualFileContentInBundle);

                this.actualContents.remove(expectedDirectoryInBundle + "/" + inMemoryArchive.getFileName());
            }
        }

        Assert.assertTrue(this.actualContents.isEmpty());
    }
}
