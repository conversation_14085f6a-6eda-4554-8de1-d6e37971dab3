package integration.steps;

import fbg.configuration.ElasticSearchConfiguration;
import fbg.generated.elasticsearch.model.ArchiveContent;
import fbg.service.ElasticSearchException;
import fbg.service.ElasticSearchService;
import integration.FunctionalTestSession;
import integration.service.ArchiveGeneratorService;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.awaitility.Awaitility;
import org.awaitility.pollinterval.FibonacciPollInterval;
import org.jbehave.core.annotations.Given;
import org.jbehave.core.annotations.Then;
import org.jbehave.core.model.ExamplesTable;
import org.jbehave.core.steps.Parameters;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Steps that involve Elastic Search.
 */
public class ElasticSearchSteps {
    private static final String PATH_SEPARATOR = "/";
    private static final Logger LOGGER = LoggerFactory.getLogger(ElasticSearchSteps.class);

    private ArchiveGeneratorService archiveGeneratorService = FunctionalTestSession.getInstance().getArchiveGeneratorService();
    private ElasticSearchService elasticSearchService = FunctionalTestSession.getInstance().getElasticSearchService();

    /**
     * Search the archive for the orders.
     *
     * @param orderSearches
     */
    @Then("all the orders archives are indexed: $orderSearches")
    public void searchOrder(ExamplesTable orderSearches) {
        Awaitility.await().pollDelay(5, TimeUnit.SECONDS).atMost(1, TimeUnit.MINUTES).pollInterval(FibonacciPollInterval.fibonacci()).until(
                () -> {
                    AtomicReference<Boolean> result = new AtomicReference<>();
                    result.set(Boolean.TRUE);
                    orderSearches.getRowsAsParameters().forEach(
                            (Parameters parameters) -> {
                                String orderIdKey = parameters.valueAs("orderIdKey", String.class);
                                String fileNameKeys = parameters.valueAs("fileNameKeys", String.class);
                                final String orderId = archiveGeneratorService.getOrderId(orderIdKey);
                                final List<String> expectedFileNames = Stream.of(fileNameKeys.split(",")).map((fileNameKey)
                                        -> archiveGeneratorService.getArchive(fileNameKey).makeObjectKey()).collect(Collectors.toList());
                                final List<String> actualFileNames = elasticSearchService.getOrderDocuments(orderId)
                                                                    .stream().map(ArchiveContent::getFilePath).collect(Collectors.toList());
                                List<String> expectedFileNamesCopy = new ArrayList<>(expectedFileNames);
                                expectedFileNames.removeAll(actualFileNames);
                                actualFileNames.removeAll(expectedFileNamesCopy);
                                for (String expectedFileName : expectedFileNames) {
                                    LOGGER.error("Couldn't find expected archive {} for order {} ", expectedFileName, orderId);
                                }
                                for (String actualFileName : actualFileNames) {
                                    LOGGER.error("Found unexpected archive {} for order {} ", actualFileName, orderId);
                                }
                                boolean searchResult = expectedFileNames.isEmpty() && actualFileNames.isEmpty();
                                if (!searchResult) {
                                    result.set(Boolean.FALSE);
                                }
                            }
                    );
                    if (result.get()) {
                        LOGGER.info("Found match in order archives searches");
                    } else {
                        LOGGER.error("Found mismatch in order archives searches");
                    }

                    return result.get();
                }


        );
    }

    /**
     * Delete the files index from elasticsearch.
     */
    @Given("files index does not exist")
    public void emptyIndexes() {
        try (CloseableHttpClient httpclient = HttpClients.createDefault()) {
            URI uri = new URIBuilder(ElasticSearchConfiguration.getInstance().getEndpoint())
                    .setPath(PATH_SEPARATOR + ElasticSearchConfiguration.DOCUMENT_INDEX)
                    .build();
            HttpDelete httpDelete = new HttpDelete(uri);
            httpclient.execute(httpDelete);
        } catch (Exception e) {
            throw new ElasticSearchException(e);
        }
    }
}
