package integration;

import net.serenitybdd.jbehave.SerenityStories;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.jbehave.core.annotations.BeforeStories;


/**
 * Functional tests suite.
 */
public class FunctionalTestSuite extends SerenityStories {
    private static final Logger LOGGER = LoggerFactory.getLogger(FunctionalTestSuite.class);

    /**
     * Set up the environment for stories execution.
     */
    @BeforeStories
    public void setUp() {
        final String titleMargins = "\n=========================================================";
        final String title = "\n>>>>>>>>>>> STARTING THE FUNCTIONAL TEST SUITE";
        LOGGER.info("{}{}{}", titleMargins, title, titleMargins);
    }
}
