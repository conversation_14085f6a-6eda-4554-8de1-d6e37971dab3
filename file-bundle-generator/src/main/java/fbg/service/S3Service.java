package fbg.service;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.S3Object;
import fbg.configuration.S3Configuration;
import fbg.context.BeanFactory;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Paths;
import java.util.Date;

/**
 * Service used to interact with S3.
 */
public class S3Service {
    private static final Logger LOGGER = LoggerFactory.getLogger(S3Service.class);
    private AmazonS3 amazonS3 = BeanFactory.getInstance().getAmazonS3();

    /**
     * Get the s3 object from given bucket.
     */
    public S3Object getObjectFromBucket(String bucket, String key) {
        try {
            final GetObjectRequest getObjectRequest = new GetObjectRequest(bucket, key);
            return amazonS3.getObject(getObjectRequest);
        } catch (Exception exception) {
            LOGGER.error(String.format("Error while trying to download the key %s from the bucket %s", key, bucket));
            throw exception;
        }
    }

    /**
     * Puts a file into given bucket.
     */
    public PutObjectResult putFileToBucket(String bucket, String key, File file) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setExpirationTime(getExpiration());
        PutObjectRequest request = new PutObjectRequest(bucket, key, file).withMetadata(objectMetadata);
        return amazonS3.putObject(request);
    }

    public void uploadDocument(String bucket, ByteArrayInputStream inputStream, String fileName) throws IOException {
        String fullPath = Paths.get(fileName).toString();
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(inputStream.available());
        amazonS3.putObject(bucket, fullPath, inputStream, objectMetadata);
    }

    /**
     * Generates a pre-signed download url for an s3 object.
     */
    public URL generateDownloadUrl(String bucketName, String objectKey) {
        return amazonS3.generatePresignedUrl(bucketName, objectKey, getExpiration(), HttpMethod.GET);
    }

    private Date getExpiration() {
        return new Date(System.currentTimeMillis() + S3Configuration.getInstance().getArchiveRetentionMs());
    }
}
