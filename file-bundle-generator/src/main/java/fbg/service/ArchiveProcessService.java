package fbg.service;

import com.amazonaws.services.s3.model.S3Object;
import fbg.context.BeanFactory;
import fbg.generated.elasticsearch.model.ArchiveContent;
import fbg.parser.ArchiveParser;
import fbg.parser.ArchiveParserFactory;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Optional;

/**
 * Service for the archive processing.
 */
public class ArchiveProcessService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ArchiveProcessService.class);

    private ArchiveParserFactory archiveParserFactory = BeanFactory.getInstance().getArchiveParserFactory();

    /**
     * Process an s3Object and returns the model that should be used as document for the elastic search put.
     */
    public ArchiveContent processS3Object(S3Object s3Object) {
        ArchiveContent archiveContent = new ArchiveContent()
                .withFilePath(s3Object.getKey())
                .withS3bucket(s3Object.getBucketName());
        LOGGER.info("INSIDE processS3Object");
        final Optional<ArchiveParser> archiveParser = archiveParserFactory.getParser(s3Object.getBucketName(), s3Object.getKey());
        if (archiveParser.isPresent()) {
            archiveContent.withType(archiveParser.get().getArchiveType().name()).withOrders(new ArrayList<>(archiveParser.get().parse(s3Object)));
        } else {
            LOGGER.info("Object {} of archive {} has not any configured parser", s3Object.getKey(), s3Object.getBucketName());
        }
        if (s3Object.getObjectMetadata().getLastModified() != null) {
            archiveContent.withLastModified(
                    ZonedDateTime.ofInstant(s3Object.getObjectMetadata().getLastModified().toInstant(),
                            ZoneOffset.UTC));
        }
        return archiveContent;
    }
}
