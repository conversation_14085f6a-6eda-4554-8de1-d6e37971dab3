package fbg.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import fbg.configuration.ElasticSearchConfiguration;
import fbg.generated.elasticsearch.model.ArchiveContent;
import fbg.generated.elasticsearch.model.CountsApiResponse;
import fbg.generated.elasticsearch.model.Hit;
import fbg.generated.elasticsearch.model.SearchArchiveContentResponse;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service used to interact with elastic search.
 */
public class ElasticSearchService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ElasticSearchService.class);
    private static final String PATH_SEPARATOR = "/";
    private static final String ASSIGNMENT = ":";

    private static ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .registerModule(new JavaTimeModule());

    /**
     * Put an ArchiveContent document to elastic search.
     *
     * @param archiveContent
     * @return
     */
    public boolean putOrderDocument(ArchiveContent archiveContent) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URI uri = new URIBuilder(ElasticSearchConfiguration.getInstance().getEndpoint())
                    .setPath(PATH_SEPARATOR + ElasticSearchConfiguration.DOCUMENT_INDEX
                            + PATH_SEPARATOR + ElasticSearchConfiguration.DOCUMENT_TYPE + PATH_SEPARATOR)
                    .build();
            HttpPost httpPost = new HttpPost(uri.toString());
            StringEntity stringEntity = new StringEntity(objectMapper.writeValueAsString(archiveContent));
            httpPost.setEntity(stringEntity);
            httpPost.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
            CloseableHttpResponse response = httpClient.execute(httpPost);
            final int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == HttpStatus.SC_CREATED || statusCode == HttpStatus.SC_OK) {
                LOGGER.info("Successfully processed the file {} of bucket {},  number of orders: {}  ",
                        archiveContent.getFilePath(), archiveContent.getS3bucket(), archiveContent.getOrders().size());
                return true;
            } else {
                LOGGER.error(String.format("Error while trying to put the document to "
                                + "elastic search for file %s and bucket %s  status code %s , reasonPhrase %s ",
                        archiveContent.getFilePath(), archiveContent.getS3bucket(),
                        statusCode, response.getStatusLine().getReasonPhrase()));
                return false;
            }
        } catch (URISyntaxException | IOException e) {
            LOGGER.error(String.format("Error while trying to call elastic search "
                            + "elastic search for file %s and bucket %s ",
                    archiveContent.getFilePath(), archiveContent.getS3bucket()));
            throw new ElasticSearchException(e);
        }
    }

    /**
     * Search in elastic search for an order in the index "files".
     *
     * @param orderId order Id
     * @return list of files.
     */
    public List<ArchiveContent> getOrderDocuments(String orderId) {

        String hits = countOrders(orderId);
        LOGGER.info("Successfully counted {} files", hits);
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URI uri = new URIBuilder(ElasticSearchConfiguration.getInstance().getEndpoint())
                    .setPath(PATH_SEPARATOR + ElasticSearchConfiguration.DOCUMENT_INDEX + PATH_SEPARATOR + ElasticSearchConfiguration.SEARCH_COMMAND)
                    .addParameter(ElasticSearchConfiguration.QUERY, ElasticSearchConfiguration.SEARCH_PARAM + ASSIGNMENT + orderId)
                    .addParameter(ElasticSearchConfiguration.SIZE, hits)
                    .build();
            HttpGet httpGet = new HttpGet(uri);
            CloseableHttpResponse response = httpClient.execute(httpGet);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                SearchArchiveContentResponse searchArchiveContentResponse =
                        objectMapper.readValue(response.getEntity().getContent(), SearchArchiveContentResponse.class);
                return searchArchiveContentResponse.getHits().getHits().stream().map(Hit::getSource).collect(Collectors.toList());
            }
        } catch (Exception e) {
            throw new ElasticSearchException(e);
        }
        return Collections.emptyList();
    }

    private String countOrders(String orderId) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URI uri = new URIBuilder(ElasticSearchConfiguration.getInstance().getEndpoint())
                    .setPath(PATH_SEPARATOR + ElasticSearchConfiguration.DOCUMENT_INDEX + PATH_SEPARATOR + ElasticSearchConfiguration.COUNT_COMMAND)
                    .addParameter(ElasticSearchConfiguration.QUERY, ElasticSearchConfiguration.SEARCH_PARAM + ASSIGNMENT + orderId)
                    .build();
            HttpGet httpGet = new HttpGet(uri);
            CloseableHttpResponse response = httpClient.execute(httpGet);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                final CountsApiResponse countsApiResponse = objectMapper.readValue(response.getEntity().getContent(), CountsApiResponse.class);
                return countsApiResponse.getCount().toString();
            } else {
                throw new ElasticSearchException(String.format(" Http request for counts fails with status code %d : ",
                        response.getStatusLine().getStatusCode()));
            }
        } catch (Exception e) {
            LOGGER.error("Could not retrieve entries for order {} :", orderId);
            throw new ElasticSearchException(e);
        }

    }
}
