package fbg.parser.ingrammicro.orderstatus;

import com.amazonaws.services.s3.model.S3Object;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import fbg.configuration.FileType;
import fbg.parser.ArchiveParseException;
import fbg.parser.ArchiveParser;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Parser for the IM ORDER_STATUS_UPDATE files.
 */
public class IngramMicroOrderStatusParser implements ArchiveParser {

    private static final Logger LOGGER = LoggerFactory.getLogger(IngramMicroOrderStatusParser.class);
    private final ObjectMapper mapper = new ObjectMapper().findAndRegisterModules();

    @Override
    public Set<String> parse(S3Object s3Object) {
        LOGGER.info("Inside IngramMicroOrderStatusParser");
        String json = transformObjectToJsonString(s3Object);
        return getAllOrderCodes(json);
    }

    private Set<String> getAllOrderCodes(String json) {
        try {
            final List<OrderStatusUpdateRequest> orderStatusUpdateRequest = deserialize(json);
            return orderStatusUpdateRequest.stream().filter(item -> item.getOrderCode() != null)
                    .map(OrderStatusUpdateRequest::getOrderCode).collect(Collectors.toSet());
        } catch (Exception e) {
            LOGGER.error("Error during deserialization of string: {} ", json, e);
            throw new ArchiveParseException(e);
        }
    }

    private List<OrderStatusUpdateRequest> deserialize(String json) throws IOException {
        return mapper.readValue(json, new TypeReference<List<OrderStatusUpdateRequest>>() { });
    }

    private String transformObjectToJsonString(S3Object s3Object) {
        return new BufferedReader(new InputStreamReader(s3Object.getObjectContent(), StandardCharsets.UTF_8))
                .lines().collect(Collectors.joining());
    }

    @Override
    public FileType getArchiveType() {
        return FileType.ORDER_STATUS;
    }
}
