package fbg.parser;

import com.amazonaws.services.s3.model.S3Object;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamConstants;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.IOException;
import java.util.Set;
import java.util.concurrent.ConcurrentSkipListSet;

/**
 * Abstract class for {@See ArchiveParser} used to parse xml files and
 * extract all the contents in a tag and return it as a Set.
 */
public abstract class XmlSingleTagParser implements ArchiveParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(XmlSingleTagParser.class);

    @Override
    public Set<String> parse(S3Object s3Object) {
        XMLStreamReader reader = null;
        Set<String> orderIds = new ConcurrentSkipListSet<>();
        try {
            reader = XMLInputFactory.newInstance().createXMLStreamReader(s3Object.getObjectContent());
            while (reader.hasNext()) {
                if (reader.next() == XMLStreamConstants.START_ELEMENT && getTag().equals(reader.getLocalName())) {
                    try {
                        if (reader.next() == XMLStreamConstants.CHARACTERS
                                && StringUtils.isNotEmpty(reader.getText())) {
                            orderIds.add(extractOrderId(reader.getText()));
                        } else {
                            LOGGER.error("Couldn't extract the order id for  object {} of bucket {}",
                                    s3Object.getKey(), s3Object.getBucketName());
                        }
                    } catch (Exception exception) {
                        LOGGER.error(String.format("Exception while trying to parse the object %s of bucket %s",
                                s3Object.getKey(), s3Object.getBucketName()), exception);
                    }
                }
            }
        } catch (XMLStreamException e) {
            throw new ArchiveParseException(e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (XMLStreamException e) {
                    // ignore
                }
            }
            if (s3Object != null) {
                try {
                    s3Object.close();
                } catch (IOException e) {
                    LOGGER.error("Couldn't close the s3Object stream for object {} of bucket {}",
                            s3Object.getKey(), s3Object.getBucketName());
                }
            }
        }
        return orderIds;
    }

    /**
     * Subclasses can change the value extracted from the xml.
     * @param orderId
     * @return
     */
    protected abstract String extractOrderId(String orderId);

    /**
     * Tag used to extract the values.
     * @return
     */
    protected abstract String getTag();
}
