package fbg.parser.plbc;

import com.amazonaws.services.s3.model.S3Object;
import com.fasterxml.jackson.databind.ObjectMapper;
import fbg.configuration.FileType;
import fbg.parser.ArchiveParseException;
import fbg.parser.ArchiveParser;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

public class ParcelLabPayloadsParser implements ArchiveParser {

    private static final Logger LOGGER = LoggerFactory.getLogger(ParcelLabPayloadsParser.class);

    private final ObjectMapper mapper = new ObjectMapper().findAndRegisterModules();

    @Override
    public Set<String> parse(S3Object s3Object) {
        LOGGER.info("Inside ParcelLabPayloadsParser");
        String json = transformObjectToJsonString(s3Object);
        return getAllOrderCodes(json);
    }

    @Override
    public FileType getArchiveType() {
        return FileType.PARCELLAB_PAYLOADS;
    }

    private Set<String> getAllOrderCodes(String json) {
        try {
            ParcelLabOrder parcelLabOrder = deserialize(json);
            return parcelLabOrder == null || parcelLabOrder.getOrderId() == null
                ? Collections.emptySet()
                : Collections.singleton(parcelLabOrder.getOrderId());
        } catch (Exception e) {
            LOGGER.error("Error during deserialization of string: {} ", json, e);
            throw new ArchiveParseException(e);
        }
    }

    private ParcelLabOrder deserialize(String json) throws IOException {
        return mapper.readValue(json, ParcelLabOrder.class);
    }

    private String transformObjectToJsonString(S3Object s3Object) {
        return new BufferedReader(new InputStreamReader(s3Object.getObjectContent(), StandardCharsets.UTF_8))
            .lines()
            .collect(Collectors.joining());
    }

}
