package fbg.parser.hermes;

import com.amazonaws.services.s3.model.S3Object;
import fbg.parser.ArchiveParseException;
import fbg.parser.ArchiveParser;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Base parser for all the hermes tsv file type (e.g. fakt,pod,returns ) than can be used also by different warehouse.
 */
public abstract class HermesBaseTsvParser implements ArchiveParser {
    static final String FIELD_SEPARATOR = "\t";

    private static final Logger LOGGER = LoggerFactory.getLogger(HermesBaseTsvParser.class);
    private Predicate<String> footerFilter = (line) -> !line.startsWith("FOOTER");

    /**
     * Read an Hermes tsv file and let the lineHandler to parse the line and collect the order id.
     *
     * @param s3Object
     * @param lineHandler
     * @return
     */
    Set<String> parse(S3Object s3Object, Function<String, Optional<String>> lineHandler) {
        try (BufferedReader bufferedInputStream = new BufferedReader(new InputStreamReader(s3Object.getObjectContent()))) {
            return bufferedInputStream.lines().parallel().filter(footerFilter).map((String line) -> {
                try {
                    final Optional<String> orderId = lineHandler.apply(line);
                    if (orderId.isPresent()) {
                        return orderId.get();
                    } else {
                        LOGGER.error("Couldn't extract the order id for line '{}' of object {} of bucket {}",
                                line, s3Object.getKey(), s3Object.getBucketName());
                    }
                } catch (Exception e) {
                    LOGGER.error(String.format("Exception while trying to parse the line %s of object %s of bucket %s",
                            line, s3Object.getKey(), s3Object.getBucketName()), e);
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toSet());
        } catch (Exception e) {
            throw new ArchiveParseException(e);
        } finally {
            if (s3Object != null) {
                try {
                    s3Object.close();
                } catch (IOException e) {
                    LOGGER.error("Couldn't close the s3Object stream for object {} of bucket {}",
                            s3Object.getKey(), s3Object.getBucketName());
                }
            }
        }
    }
}
