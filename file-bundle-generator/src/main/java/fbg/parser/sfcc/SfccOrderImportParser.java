package fbg.parser.sfcc;

import com.amazonaws.services.s3.model.S3Object;
import fbg.configuration.FileType;
import fbg.parser.ArchiveParseException;
import fbg.parser.ArchiveParser;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamConstants;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.IOException;
import java.util.Collections;
import java.util.Set;

/**
 * Parser for sfcc orders.
 */
public class SfccOrderImportParser implements ArchiveParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(SfccOrderImportParser.class);
    private static final String ORDER_TAG = "order";
    private static final String ORDER_NO_ATTRIBUTE = "order-no";

    @Override
    public Set<String> parse(S3Object s3Object) {
        XMLStreamReader reader = null;
        try {
            reader = XMLInputFactory.newInstance().createXMLStreamReader(s3Object.getObjectContent());
            while (reader.hasNext()) {
                if (reader.next() == XMLStreamConstants.START_ELEMENT
                        && ORDER_TAG.equals(reader.getLocalName())) {
                    String orderNo = reader.getAttributeValue(null, ORDER_NO_ATTRIBUTE);
                    if (StringUtils.isNotEmpty(orderNo)) {
                        return Collections.singleton(orderNo);
                    }
                }
            }
            throw new ArchiveParseException(String.format("Couldn't find an order for archive %s of bucket %s",
                    s3Object.getKey(), s3Object.getBucketName()));
        } catch (Exception e) {
            throw new ArchiveParseException(e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (XMLStreamException e) {
                    // ignore
                }
            }
            if (s3Object != null) {
                try {
                    s3Object.close();
                } catch (IOException e) {
                    LOGGER.error("Couldn't close the s3Object stream for object {} of bucket {}",
                            s3Object.getKey(), s3Object.getBucketName());
                }
            }
        }
    }

    @Override
    public FileType getArchiveType() {
        return FileType.ORDER;
    }
}
