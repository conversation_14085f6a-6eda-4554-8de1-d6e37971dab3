package fbg.parser;

import fbg.configuration.HwcConfiguration;
import fbg.configuration.ImwcConfiguration;
import fbg.configuration.OmsConfiguration;
import fbg.configuration.OsimcConfiguration;
import fbg.configuration.PlbcConfiguration;
import fbg.configuration.TbcConfiguration;
import fbg.parser.hermes.HermesOrderExportParser;
import fbg.parser.hermes.HermesOrderFaktParser;
import fbg.parser.hermes.HermesOrderPodParser;
import fbg.parser.hermes.HermesOrderReturnParser;
import fbg.parser.hermes.HermesOrderUpdateParser;
import fbg.parser.ingrammicro.orderstatus.IngramMicroOrderStatusParser;
import fbg.parser.ingrammicro.returns.IngramMicroOrderReturnParser;
import fbg.parser.plbc.ParcelLabPayloadsParser;
import fbg.parser.sfcc.SfccOrderImportParser;
import fbg.parser.sfcc.SfccOrderStatusExportParser;
import fbg.parser.tradebyte.TradebyteOrderImportParser;
import fbg.parser.tradebyte.TradebyteOrderStatusExportParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

/**
 * Factory class that used to return parser that should be used.
 */
public class ArchiveParserFactory {
    private static final Logger LOGGER = LoggerFactory.getLogger(ArchiveParserFactory.class);

    private HermesOrderReturnParser hermesOrderReturnParser = new HermesOrderReturnParser();
    private HermesOrderUpdateParser hermesFaktParser = new HermesOrderFaktParser();
    private HermesOrderUpdateParser hermesPodParser = new HermesOrderPodParser();
    private HermesOrderExportParser hermesOrderExportParser = new HermesOrderExportParser();
    private SfccOrderImportParser sfccOrderImportParser = new SfccOrderImportParser();
    private SfccOrderStatusExportParser sfccOrderStatusExportParser = new SfccOrderStatusExportParser();
    private TradebyteOrderStatusExportParser tradebyteOrderStatusExportParser = new TradebyteOrderStatusExportParser();
    private TradebyteOrderImportParser tradebyteOrderImportParser = new TradebyteOrderImportParser();
    private IngramMicroOrderReturnParser ingramMicroOrderReturnParser = new IngramMicroOrderReturnParser();
    private IngramMicroOrderStatusParser ingramMicroOrderStatusParser = new IngramMicroOrderStatusParser();
    private ParcelLabPayloadsParser parcelLabPayloadsParser = new ParcelLabPayloadsParser();

    /**
     * Return the parser depending on the bucket name , file name and path.
     * Add the more frequent archives first.
     */
    public Optional<ArchiveParser> getParser(String bucketName, String filePath) {
        LOGGER.info("Getparser for: {}, {}", bucketName, filePath);
        Optional<ArchiveParser> archiveParser = Optional.empty();
        if (OmsConfiguration.getInstance().getBucketName().equals(bucketName)) {
            final String folder = filePath.substring(0, filePath.lastIndexOf('/'));
            switch (folder) {
                case OmsConfiguration.SFCC_ORDER_FOLDER:
                    archiveParser = Optional.of(this.sfccOrderImportParser);
                    break;
                case OmsConfiguration.SFCC_ORDER_STATUS_FOLDER:
                    archiveParser = Optional.of(this.sfccOrderStatusExportParser);
                    break;
                case OmsConfiguration.TB_ORDER_STATUS_FOLDER:
                    archiveParser = Optional.of(this.tradebyteOrderStatusExportParser);
                    break;
                default:
                    throw new ArchiveParseException(String.format("Couldn't find the parser for oms archive %s ", filePath));
            }
        } else if (HwcConfiguration.getInstance().getBucketName().equals(bucketName)) {
            final String folder = filePath.substring(0, filePath.lastIndexOf('/'));
            switch (folder) {
                case HwcConfiguration.ORDER_EXPORT_FOLDER:
                    archiveParser = Optional.of(this.hermesOrderExportParser);
                    break;
                case HwcConfiguration.FAKT_FOLDER:
                    archiveParser = Optional.of(this.hermesFaktParser);
                    break;
                case HwcConfiguration.POD_FOLDER:
                    archiveParser = Optional.of(this.hermesPodParser);
                    break;
                case HwcConfiguration.RETURNS_FOLDER:
                    archiveParser = Optional.of(this.hermesOrderReturnParser);
                    break;
                default:
                    throw new ArchiveParseException(String.format("Couldn't find the parser for hwc archive %s ", filePath));
            }
        } else if (ImwcConfiguration.getInstance().getBucketName().equals(bucketName)) {
            if (filePath.startsWith(ImwcConfiguration.ORDER_EXPORT_PREFIX)) {
                archiveParser = Optional.of(this.hermesOrderExportParser);
            } else if (filePath.startsWith(ImwcConfiguration.FAKT_FILE_PREFIX)) {
                archiveParser = Optional.of(this.hermesFaktParser);
            } else if (filePath.startsWith(ImwcConfiguration.POD_FILE_PREFIX)) {
                archiveParser = Optional.of(this.hermesPodParser);
            } else if (filePath.startsWith(ImwcConfiguration.RETURNS_FILE_PREFIX)) {
                archiveParser = Optional.of(this.hermesOrderReturnParser);
            }
        } else if (isTbcBucket(bucketName) && isFileInTBOrderFolder(filePath)) {
            archiveParser = Optional.of(this.tradebyteOrderImportParser);
        } else if (OsimcConfiguration.getInstance().getBucketName().equals(bucketName)) {
            if (filePath.startsWith(OsimcConfiguration.RETURNS_FILE_PREFIX)) {
                archiveParser = Optional.of(this.ingramMicroOrderReturnParser);
            } else if (filePath.startsWith(OsimcConfiguration.ORDER_STATUS_FILE_PREFIX)) {
                archiveParser = Optional.of(this.ingramMicroOrderStatusParser);
            }
        } else if (PlbcConfiguration.getInstance().getBucketName().equals(bucketName)) {
            if (filePath.startsWith(PlbcConfiguration.PAYLOADS_FOLDER)) {
                archiveParser = Optional.of(parcelLabPayloadsParser);
            }
        }
        LOGGER.info("Parser returned");
        return archiveParser;
    }

    private boolean isTbcBucket(String bucketName) {
        return TbcConfiguration.getInstance().getBucketName().equals(bucketName);
    }

    private boolean isFileInTBOrderFolder(String filePath) {
        final String folder = filePath.substring(0, filePath.lastIndexOf('/'));
        return folder.equals(TbcConfiguration.TRADEBYTE_ORDER_FOLDER);
    }
}
