package fbg.parser.tradebyte;

import com.amazonaws.services.s3.model.S3Object;
import fbg.configuration.FileType;
import fbg.parser.ArchiveParseException;
import fbg.parser.ArchiveParser;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamConstants;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.IOException;
import java.util.Set;
import java.util.concurrent.ConcurrentSkipListSet;

/**
 * Parser for tradebyte orders.
 */
public class TradebyteOrderImportParser implements ArchiveParser {
    private static final String ORDER_DATA_TAG = "ORDER_DATA";
    private static final String TB_ID_TAG = "TB_ID";
    private static final Logger LOGGER = LoggerFactory.getLogger(TradebyteOrderImportParser.class);

    @Override
    public Set<String> parse(S3Object s3Object) {
        XMLStreamReader reader = null;
        Set<String> orderIds = new ConcurrentSkipListSet<>();
        try {
            reader = XMLInputFactory.newInstance().createXMLStreamReader(s3Object.getObjectContent());
            while (reader.hasNext()) {
                if (reader.next() == XMLStreamConstants.START_ELEMENT && ORDER_DATA_TAG.equals(reader.getLocalName())) {
                    try {
                        while (reader.hasNext()) {
                            int event = reader.next();
                            if (event == XMLStreamConstants.START_ELEMENT
                                && TB_ID_TAG.equals(reader.getLocalName())
                                && reader.next() == XMLStreamConstants.CHARACTERS
                                && StringUtils.isNotEmpty(reader.getText())
                            ) {
                                orderIds.add("TB" + reader.getText());
                                break;
                            } else if (event == XMLStreamConstants.END_ELEMENT
                                && ORDER_DATA_TAG.equals(reader.getLocalName())) {
                                LOGGER.error("Couldn't extract the order id for  object {} of bucket {}",
                                    s3Object.getKey(), s3Object.getBucketName());
                                break;
                            }
                        }
                    } catch (Exception exception) {
                        LOGGER.error(String.format("Exception while trying to parse the object %s of bucket %s",
                            s3Object.getKey(), s3Object.getBucketName()), exception);
                    }
                }
            }
        } catch (XMLStreamException e) {
            String message = String.format("S3 file parsing error happened: bucket=%s, object=%s",
                s3Object.getBucketName(), s3Object.getKey());
            throw new ArchiveParseException(message, e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (XMLStreamException e) {
                    // ignore
                }
            }
            if (s3Object != null) {
                try {
                    s3Object.close();
                } catch (IOException e) {
                    LOGGER.error("Couldn't close the s3Object stream for object {} of bucket {}",
                        s3Object.getKey(), s3Object.getBucketName());
                }
            }
        }
        return orderIds;
    }

    @Override
    public FileType getArchiveType() {
        return FileType.ORDER;
    }
}
