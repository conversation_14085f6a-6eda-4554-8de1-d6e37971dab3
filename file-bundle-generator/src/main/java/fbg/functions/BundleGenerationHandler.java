package fbg.functions;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BundleGenerationHandler implements RequestHandler<APIGatewayProxyRequestEvent, APIGatewayProxyResponseEvent> {
    private static final Logger LOGGER = LoggerFactory.getLogger(BundleGenerationHandler.class);
    private static final String ORANGE_GENERATOR_API_PATH = "orange";

    @Override
    public APIGatewayProxyResponseEvent handleRequest(APIGatewayProxyRequestEvent event, Context context) {
        LOGGER.info("Received event: {}, path: {}", event, event.getPath());
        String orderId;

        if (event.getPath().contains(ORANGE_GENERATOR_API_PATH)) {
            LOGGER.info("Start processing orange file download params{}", event.getPathParameters().toString());
            try {
                orderId = event.getPathParameters().get("orderId");
                LOGGER.info("Start processing orange file download for orderId {}", orderId);
                OrangeFileGenerationHandler orangeFileGenerationHandler = new OrangeFileGenerationHandler();
                return orangeFileGenerationHandler.orangeFileHandleRequest(orderId);
            } catch (Exception ex) {
                LOGGER.error("Exception in orange part {}", ex.getMessage());
            }
        } else {
            orderId = event.getPathParameters().get("orderBundle");
            LOGGER.info("Start processing order bundle file download for orderId {}", orderId);

            OrderBundleGenerationHandler orderBundleGenerationHandler = new OrderBundleGenerationHandler();
            return orderBundleGenerationHandler.orderBundleHandleRequest(orderId);
        }
        return null;
    }

}
