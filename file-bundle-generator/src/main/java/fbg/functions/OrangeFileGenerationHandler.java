package fbg.functions;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.amazonaws.util.IOUtils;
import fbg.configuration.S3OrangeFileConfig;
import fbg.context.BeanFactory;
import fbg.invoce.DownloadFile;
import fbg.service.ZipCreator;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;

/**
 * Handles an APIGateway request and:
 * - Download orange files as zip file if there are more than one or as a pdf if it's only one file.
 * - Uploads the orange file to S3
 * - Provides a pre-signed download url for Zip file.
 */
public class OrangeFileGenerationHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrangeFileGenerationHandler.class);
    private final AmazonS3 amazonS3Client = BeanFactory.getInstance().getAmazonS3();
    private final String bucket = S3OrangeFileConfig.getInstance().getBucketName();
    private final ZipCreator zipCreator = BeanFactory.getInstance().getZipCreator();

    public APIGatewayProxyResponseEvent orangeFileHandleRequest(String orderId) {
        APIGatewayProxyResponseEvent responseEvent = new APIGatewayProxyResponseEvent();
        List<String> fileNames;
        DownloadFile orangeFile;

        try {
            LOGGER.info("Start creating orange file for orderId {} if is exists", orderId);

            if (doesFileExistAndNotEmpty(orderId)) {
                fileNames = retrieveFilesInS3BucketDir(orderId);

                if (fileNames.size() > 1) {
                    List<DownloadFile> fileContents = getS3FileContents(fileNames);
                    //zip orange file
                    byte[] content = zipCreator.bundleZip(fileContents);
                    orangeFile = new DownloadFile(orderId + ".zip", content);
                    LOGGER.info("Create zip orange file by id {}, name {}", orderId, orangeFile.getFileName());
                    createResponseEvent(responseEvent, Base64.getEncoder().encodeToString(orangeFile.getContent()),
                            "application/zip", HttpStatus.SC_OK);
                } else {
                    // pdf orange file
                    String fileName = fileNames.stream().findFirst().orElseThrow(RuntimeException::new);
                    orangeFile = getFileViaS3(fileName, fileName.substring(fileName.indexOf("/")));
                    LOGGER.info("Create PDF orange file by id {}, fileName {}", orderId, fileName);
                    createResponseEvent(responseEvent, Base64.getEncoder().encodeToString(orangeFile.getContent()),
                            "application/pdf", HttpStatus.SC_OK);
                }
            } else {
                LOGGER.warn("File doesn't exist for order Id {}", orderId);
                String body = "The orange file does not exist for order id " + orderId + ". Please retry again later.";
                createResponseEvent(responseEvent, body, "text/plain", HttpStatus.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            LOGGER.error("Error handling request: ", e);
            responseEvent.setStatusCode(HttpStatus.SC_INTERNAL_SERVER_ERROR);
        }

        return responseEvent;
    }

    private List<String> retrieveFilesInS3BucketDir(String orderId) {
        List<String> fileNames = new ArrayList<>();
        LOGGER.info("Listing of objects in folder {}", orderId);

        ObjectListing objectList = amazonS3Client.listObjects(bucket, orderId + "/");
        List<S3ObjectSummary> objectSummeryList = objectList.getObjectSummaries();

        for (S3ObjectSummary summary : objectSummeryList) {
            if (summary.getKey().endsWith(".pdf")) {
                fileNames.add(summary.getKey());
            }
        }

        if (fileNames.isEmpty()) {
            throw new RuntimeException("retrieve files in S3 bucket directory is empty");
        }

        return fileNames;
    }

    private List<DownloadFile> getS3FileContents(final List<String> fileNames) throws IOException {
        final List<DownloadFile> fileContents = new ArrayList<>(fileNames.size());
        String file;

        for (String fileName : fileNames) {
            LOGGER.info("Lets download {}", fileName);
            file = fileName.substring(fileName.indexOf("/"));
            fileContents.add(getFileViaS3(fileName, file));
        }

        return fileContents;
    }

    private DownloadFile getFileViaS3(String fileName, String finalFile) throws IOException {

        LOGGER.info("Getting file {} from S3", fileName);

        S3Object object = amazonS3Client.getObject(bucket, fileName);
        S3ObjectInputStream objectContent = object.getObjectContent();
        byte[] bytes = IOUtils.toByteArray(objectContent);
        return new DownloadFile(finalFile, bytes);
    }

    private boolean doesFileExistAndNotEmpty(String orderId) {
        ListObjectsV2Result result = amazonS3Client.listObjectsV2(bucket, orderId + "/");
        return result.getKeyCount() > 0;
    }

    private void createResponseEvent(APIGatewayProxyResponseEvent responseEvent, String responseBody, String contentType, int statusCode) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpHeaders.CONTENT_TYPE, contentType);
        responseEvent.setHeaders(headers);
        responseEvent.setBody(responseBody);
        responseEvent.setStatusCode(statusCode);
    }
}
