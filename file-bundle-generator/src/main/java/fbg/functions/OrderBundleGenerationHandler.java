package fbg.functions;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.amazonaws.services.s3.model.S3Object;
import fbg.configuration.BundleDirectoryConfiguration;
import fbg.configuration.S3Configuration;
import fbg.context.BeanFactory;
import fbg.generated.elasticsearch.model.ArchiveContent;
import fbg.service.ElasticSearchService;
import fbg.service.S3Service;
import fbg.service.ZipCreator;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import fbg.configuration.BundleDirectoryConfiguration;
import fbg.configuration.S3Configuration;
import fbg.context.BeanFactory;
import fbg.service.ElasticSearchService;
import fbg.service.S3Service;
import fbg.service.ZipCreator;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.zip.ZipOutputStream;

/**
 * Handles an APIGateway request and:
 * - Creates an order bundle as a zip file
 * - Uploads the bundle to S3
 * - Provides a pre-signed download url.
 */
public class OrderBundleGenerationHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderBundleGenerationHandler.class);

    private S3Service s3Service = BeanFactory.getInstance().getS3Service();
    private ElasticSearchService elasticSearchService = BeanFactory.getInstance().getElasticSearchService();
    private ZipCreator zipCreator = BeanFactory.getInstance().getZipCreator();

    public APIGatewayProxyResponseEvent orderBundleHandleRequest(String orderId) {
        APIGatewayProxyResponseEvent responseEvent = new APIGatewayProxyResponseEvent();

        List<ArchiveContent> archiveContents = elasticSearchService.getOrderDocuments(orderId);
        try {
            File archiveFile = createZipArchive(orderId, archiveContents);

            s3Service.putFileToBucket(S3Configuration.getInstance().getBucketName(), archiveFile.getName(), archiveFile);
            responseEvent.setHeaders(Collections.singletonMap(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_OCTET_STREAM.getMimeType()));
            responseEvent.setStatusCode(HttpStatus.SC_OK);
            responseEvent.setBody(
                    s3Service.generateDownloadUrl(S3Configuration.getInstance().getBucketName(), archiveFile.getName()).toURI().toString());

            if (!archiveFile.delete()) {
                LOGGER.error("Could not delete temporary file {}.", archiveFile.getName());
            }
            LOGGER.info("Successfully created the bundle for orderId: {} , number of files: {} ", orderId, archiveContents.size());
        } catch (Exception e) {
            LOGGER.error("Error handling request: ", e);
            responseEvent.setStatusCode(HttpStatus.SC_INTERNAL_SERVER_ERROR);
        }

        return responseEvent;
    }

    private File createZipArchive(String orderId, List<ArchiveContent> archiveContents) throws IOException {
        File archiveFile = File.createTempFile(orderId + "-", "-FBG.zip");
        Set<String> uniqueArchivePaths = new HashSet<>();
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(archiveFile))) {
            for (ArchiveContent archiveContent : archiveContents) {
                String bucket = archiveContent.getS3bucket();
                String filePath = archiveContent.getFilePath();
                LOGGER.info("Trying to bundle bucket {}, filePath {}", bucket, filePath);
                String pathInBundle = BundleDirectoryConfiguration
                        .getBundleDirectoryConfiguration(archiveContent.getType(), archiveContent.getS3bucket(), getParent(filePath))
                        .map(BundleDirectoryConfiguration::getBundleDirectory)
                        .orElse(BundleDirectoryConfiguration.UNKNOWN_DIRECTORY);
                String archivePath = pathInBundle + new File(filePath).getName();
                if (uniqueArchivePaths.add(archivePath)) {
                    S3Object object = s3Service.getObjectFromBucket(bucket, filePath);
                    zipCreator.addToArchive(zipOutputStream, object.getObjectContent(), archivePath);
                }
            }
        }
        return archiveFile;
    }

    private String getParent(String filePath) {
        File file = new File(filePath);
        if (file.getParent() == null) {
            return null;
        }
        while (file.getParent() != null){
            file = file.getParentFile();
        }
        return file.getName();
    }

}
