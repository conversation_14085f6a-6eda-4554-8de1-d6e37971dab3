package order.info.service;

import order.info.exception.OmsApiException;
import order.info.service.graphql.GraphQLResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatusCode;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import order.info.service.graphql.GraphqlRequest;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;

import static reactor.util.retry.Retry.backoff;

public class OmsApiGraphQLServiceImpl implements OmsApiGraphQLService {

    private static final long MAX_RETRIES = 3;
    private final Duration backoff = Duration.ofMillis(1000);
    private static final Logger LOGGER = LoggerFactory.getLogger(OmsApiGraphQLServiceImpl.class);
    private final WebClient omsApiClient;

    public OmsApiGraphQLServiceImpl(WebClient omsApiClient) {
        this.omsApiClient = omsApiClient;
    }

    /**
     * Method to execute graphql queries.
     *
     * @param query query to execute.
     * @param clazz class of the return type.
     * @param <T>   type of the return class.
     * @return Graphql response parsed into desired class.
     */
    @Override
    public <T> T executeQuery(String query, Map<String, String> variables, String resolver,
                              ParameterizedTypeReference<GraphQLResponse<T>> clazz) {
        var params = new GraphqlRequest(query, variables);
        return omsApiClient.post()
            .body(Mono.just(params), clazz)
            .retrieve()
            .onStatus(HttpStatusCode::isError, response -> {
                response.bodyToMono(String.class)
                    .publishOn(Schedulers.single())
                    .subscribe(body -> LOGGER.warn("{} responded with {}: {}\nBody: {}", "oms", params,
                        response.statusCode(), body));
                String message = "Query failed with status %s: %s".formatted(response.statusCode(), query);
                return Mono.error(new OmsApiException(message));
            }).bodyToMono(clazz)
            .filter(Objects::nonNull)
            .flatMap(this::failOnErrorPresent)
            .map(GraphQLResponse::data)
            .map(response -> response.get(resolver))
            .retryWhen(backoff(MAX_RETRIES, backoff))
            .block();
    }

    private <T> Mono<GraphQLResponse<T>> failOnErrorPresent(GraphQLResponse<T> response) {
        if (!CollectionUtils.isEmpty(response.errors())) {
            return Mono.error(new OmsApiException("Response has errors: " + response.errors()));
        }
        return Mono.just(response);
    }
}
