package order.info.service;

import order.info.converter.OrderMapper;
import order.info.model.Order;
import order.info.viewModel.OrderView;
import order.info.viewModel.FinancialView;
import order.info.viewModel.GiftCardsPaymentView;
import order.info.viewModel.ChargeView;
import order.info.viewModel.ShippingAddressView;
import order.info.viewModel.ShipmentView;
import order.info.viewModel.OrderLineView;
import order.info.viewModel.OrderLineItemView;
import order.info.viewModel.ItemView;
import order.info.viewModel.PaymentView;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class OrderMapperTest {

    @Test
    public void toOrderModel_givenOrderViewModel_returnOrderModel() {
        OrderView orderView = new OrderView(
            "1",
            ZonedDateTime.parse("2024-05-01T19:27:50Z"),
            ZonedDateTime.parse("2024-11-01T19:27:50Z"),
            "checkout",
            "market",
            "store",
            "maxStatus",
            "currency",
            "STOREFRONT",
            new FinancialView(BigDecimal.TEN,
                BigDecimal.ONE,
                new PaymentView("type", "paymentType", "processorId", "subMethodName", "subMethodType"),
                new GiftCardsPaymentView(List.of("GiftCardNumber")),
                List.of(new ChargeView("description", BigDecimal.TEN, false, "title", "type"))),
            new ShippingAddressView("zipCode"),
            List.of(new ShipmentView(1, "carrier", "trackingNumber", "returnTrackingNumber", LocalDateTime.now())),
            new OrderLineView(List.of(new OrderLineItemView(1, List.of(new ItemView("ean", "brand", "description", BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE, "status", "fulfilmentNode")))))
        );

        Order order = OrderMapper.toOrderModel(orderView);

        assertEquals(orderView.id(), order.orderId());
        assertEquals("2024-05-01T21:27:50", order.dateOrdered());
        assertEquals("2024-11-01T20:27:50", order.lastAmended());
        assertEquals(orderView.checkout(), order.checkout());
        assertEquals(orderView.market(), order.market());
        assertEquals(orderView.channelType().toLowerCase(), order.channel());
        assertEquals(orderView.store(), order.store());
        assertEquals(orderView.maxStatus(), order.orderMaxStatus());
        assertEquals(orderView.currency(), order.currency());
        assertEquals(orderView.shippingAddressView().zipCode(), order.postCode());
        assertEquals(orderView.orderLinesView().items().get(0).orderLineNumber(), order.orderLines().get(0).orderLineNumber());
        assertEquals(orderView.shipments().get(0).carrier(), order.shipments().get(0).carrier());
        assertFinancials(orderView, order);
    }

    private static void assertFinancials(OrderView orderView, Order order) {
        assertEquals(orderView.financials().grandTotal(), order.grandTotal());
        assertEquals(orderView.financials().originalTotal(), order.originalTotal());
        assertEquals(orderView.financials().paymentView().type(), order.payments().getFirst().paymentType());
        assertEquals(orderView.financials().paymentView().paymentTypeName(), order.payments().getFirst().type());
        assertEquals(orderView.financials().paymentView().processorId(), order.payments().getFirst().processorId());
        assertEquals(orderView.financials().paymentView().subMethodId(), order.payments().getFirst().subMethodName());
        assertEquals(orderView.financials().paymentView().subMethodType(), order.payments().getFirst().subMethod());

        assertEquals(orderView.financials().chargeViews().getFirst().type(), order.charges().getFirst().type());
        assertEquals(orderView.financials().chargeViews().getFirst().description(), order.charges().getFirst().description());
        assertEquals(orderView.financials().chargeViews().getFirst().amount(), order.charges().getFirst().pricePaid());
        assertEquals(orderView.financials().chargeViews().getFirst().refunded(), order.charges().getFirst().refunded());
    }
}