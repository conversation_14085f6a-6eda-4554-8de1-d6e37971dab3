package order.info.functions;


import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import order.info.exception.OrderDataNotFoundException;
import order.info.service.OrderGateway;
import order.info.service.OrderGatewayImpl;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Connection;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderViewInfoHandlerTest {
    @Mock
    private APIGatewayProxyRequestEvent event;

    @Mock
    private Connection conn;

    @Mock
    private OrderGateway orderGateway;

    @Mock
    private OrderGatewayImpl orderGatewayImpl;

    @InjectMocks
    OrderInfoHandler orderInfoHandler;

    private static final String ORDER_ID = "myOrderId";
    private static final String ORDER_ID_NOT_FOUND = "myOrderId2";
    private static final String ORDER_ID_INTERNAL_ERROR = "myOrderId3";

    JSONObject order;
    JSONArray shipments;
    JSONArray payments;
    JSONArray orderLines;
    JSONArray charges;

    @BeforeEach
    public void setUp() {
        order = new JSONObject();
        shipments = new JSONArray();
        payments = new JSONArray();
        orderLines = new JSONArray();
        charges = new JSONArray();

        JSONObject shipment = new JSONObject();
        shipment.put("shipment", "shipment");
        shipments.put(shipment);

        JSONObject payment = new JSONObject();
        payment.put("payment", "payment");
        payments.put(payment);

        JSONObject orderLine = new JSONObject();
        orderLine.put("orderLine", "orderLine");
        orderLines.put(orderLine);

        JSONObject charge = new JSONObject();
        charge.put("charge", "charge");
        charges.put(charge);

        order.put("orderId", ORDER_ID);
        order.put("shipments", shipments);
        order.put("payments", payments);
        order.put("orderLines", orderLines);
        order.put("charges", charges);

    }

    @Test
    public void handleRequest_shouldSuccessfullyReturnJson() throws JsonProcessingException {
        when(event.getPathParameters()).thenReturn(Map.of("orderId", ORDER_ID));
        when(orderGatewayImpl.getOrderInfo(any(String.class))).thenReturn(order);


        String expected = "{\"charges\":[{\"charge\":\"charge\"}],\"orderLines\":[{\"orderLine\":\"orderLine\"}],\"orderId\":\"myOrderId\",\"payments\":[{\"payment\":\"payment\"}],\"shipments\":[{\"shipment\":\"shipment\"}]}";

        String actual = orderInfoHandler.handleRequest(event, null).getBody();
        assertEquals(expected, actual);
    }

    @Test
    public void handleRequest_orderNotFund_shouldReturnNotFoundResponse() throws JsonProcessingException {
        when(event.getPathParameters()).thenReturn(Map.of("orderId", ORDER_ID_NOT_FOUND));
        when(orderGatewayImpl.getOrderInfo(any(String.class))).thenThrow(new OrderDataNotFoundException("Order data not found"));

        Integer statusCode = orderInfoHandler.handleRequest(event, null).getStatusCode();
        assertEquals(statusCode, Integer.valueOf(HttpStatus.SC_NOT_FOUND));
    }

    @Test
    public void handleRequest_orderNotFund_shouldReturnInternalErrorResponse() throws JsonProcessingException {
        when(event.getPathParameters()).thenReturn(Map.of("orderId", ORDER_ID_INTERNAL_ERROR));
        when(orderGatewayImpl.getOrderInfo(any(String.class))).thenThrow(new RuntimeException("Order data not found"));

        Integer statusCode = orderInfoHandler.handleRequest(event, null).getStatusCode();
        assertEquals(statusCode, Integer.valueOf(HttpStatus.SC_INTERNAL_SERVER_ERROR));
    }

}
