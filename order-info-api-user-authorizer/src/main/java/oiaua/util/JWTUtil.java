package oiaua.util;

import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.ImmutableJWKSet;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.DefaultJOSEObjectTypeVerifier;
import com.nimbusds.jose.proc.JWSKeySelector;
import com.nimbusds.jose.proc.JWSVerificationKeySelector;
import com.nimbusds.jose.proc.SecurityContext;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import com.nimbusds.jwt.proc.ConfigurableJWTProcessor;
import com.nimbusds.jwt.proc.DefaultJWTClaimsVerifier;
import com.nimbusds.jwt.proc.DefaultJWTProcessor;
import oiaua.config.JWTConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.Instant;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.HashSet;

public class JWTUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(JWTUtil.class);
    private static final String ISSUER = "iss";
    private static final String SUBJECT = "sub";
    private static final String AUDIENCE = "aud";
    private static final String TOKEN_TYPE = "jwt";
    private static final String ALGORITHM_TYPE = "RSA";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final ConfigurableJWTProcessor<SecurityContext> jwtProcessor = new DefaultJWTProcessor<>();
    private static final JWTConfig jwtConfig = new JWTConfig();
    private static final JWTClaimsSet.Builder claimSetBuilder = new JWTClaimsSet.Builder()
            .issuer(jwtConfig.getIssuer())
            .subject(jwtConfig.getSubject())
            .audience(jwtConfig.getAudience());

    static {
        try {
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(jwtConfig.getJwtPublicKey()));
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM_TYPE);
            PublicKey publicKey = keyFactory.generatePublic(keySpec);
            RSAKey rsaKey = new RSAKey.Builder((RSAPublicKey) publicKey).build();

            JWKSet jwkSet = new JWKSet(rsaKey);
            JWKSource<SecurityContext> jwkSource = new ImmutableJWKSet<>(jwkSet);
            JWSAlgorithm expectedJWSAlg = JWSAlgorithm.RS256;
            JWSKeySelector<SecurityContext> keySelector = new JWSVerificationKeySelector<>(expectedJWSAlg, jwkSource);
            jwtProcessor.setJWSKeySelector(keySelector);

            jwtProcessor.setJWSTypeVerifier(new DefaultJOSEObjectTypeVerifier<>(new JOSEObjectType(TOKEN_TYPE)));

            jwtProcessor.setJWTClaimsSetVerifier(new DefaultJWTClaimsVerifier<>(
                    //exact match claims
                    claimSetBuilder.build(),
                    //Required claims
                    new HashSet<>(Arrays.asList(ISSUER, SUBJECT, AUDIENCE))));
        } catch (Exception e) {
            LOGGER.error("Error while configuring the JWT processor.", e);
        }
    }

    public static Date validateAndGetExpirationTime(String token) {
        try {
            JWTClaimsSet expectedClaimsSet = claimSetBuilder.build();
            token = token.replace(BEARER_PREFIX, "");
            JWTClaimsSet claimsSet = jwtProcessor.process(token, null);

            if (expectedClaimsSet.getAudience().equals(claimsSet.getAudience()) && expectedClaimsSet.getSubject().equals(claimsSet.getSubject())
                    && expectedClaimsSet.getIssuer().equals(claimsSet.getIssuer())) {
                return claimsSet.getExpirationTime();
            }
        } catch (Exception e) {
            LOGGER.error("Error while getting JWT sub claim.", e);
        }
        return null;
    }

    public static String generateToken() throws Exception {
        try {
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(jwtConfig.getJwtPrivateKey()));
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM_TYPE);
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

            JWSHeader header = new JWSHeader.Builder(JWSAlgorithm.RS256)
                    .type(JOSEObjectType.JWT)
                    .build();
            JWTClaimsSet payload = claimSetBuilder
                    .expirationTime(Date.from(Instant.now().plusSeconds(jwtConfig.getTokenValidTimeInSeconds())))
                    .build();

            SignedJWT signedJWT = new SignedJWT(header, payload);
            signedJWT.sign(new RSASSASigner(privateKey));
            return signedJWT.serialize();
        } catch (Exception e) {
            LOGGER.error("Error while generating JWT.", e);
            throw new Exception();
        }
    }
}