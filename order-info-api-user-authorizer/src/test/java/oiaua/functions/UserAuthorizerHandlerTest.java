package oiaua.functions;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.amazonaws.services.lambda.runtime.events.IamPolicyResponse;
import oiaua.util.JWTUtil;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import uk.org.webcompere.systemstubs.environment.EnvironmentVariables;
import uk.org.webcompere.systemstubs.jupiter.SystemStub;
import uk.org.webcompere.systemstubs.jupiter.SystemStubsExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mockConstruction;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith({MockitoExtension.class, SystemStubsExtension.class})
public class UserAuthorizerHandlerTest {

    private static final String JWT_GENERATOR_API_PATH = "login";
    private static final String JWT_VALIDATOR_API_PATH = "orderId";
    private static final String ERROR_MESSAGE = "Error while generating token";
    private static final String SUCCESS_MESSAGE = "Success";
    private static final String EXPIRED_MESSAGE = "Token expired";
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String AUTHORIZED = "Allow";
    private static final String DENIED = "Deny";
    private static final String MESSAGE = "message";
    private static final String AWS_REGION = "region";
    private static final String ACTION = "execute-api:Invoke";
    private static final String RESOURCE = "*";
    private static final String NULL_INVALID_FORMAT_TOKEN_MESSAGE = "Token is null or it has an invalid format";

    @Mock
    private Context context;

    private UserAuthorizerHandler userAuthorizerHandler;

    /**
     * Setup environment variables.
     */
    @SystemStub
    private EnvironmentVariables environmentVariables = new EnvironmentVariables("AWS_REGION", AWS_REGION);

    @BeforeEach
    public void setUp() {
        userAuthorizerHandler = new UserAuthorizerHandler();
    }

    @Test
    public void testHandleRequestWithJWTGeneratorPath_success() throws Exception {
        //arrange
        APIGatewayProxyRequestEvent event = new APIGatewayProxyRequestEvent();
        event.setPath(JWT_GENERATOR_API_PATH);

        APIGatewayProxyResponseEvent expectedResponse = new APIGatewayProxyResponseEvent()
            .withStatusCode(200)
            .withBody(SUCCESS_MESSAGE);

        try (MockedConstruction<JWTGeneratorHandler> mockedConstruction = mockConstruction(JWTGeneratorHandler.class,
            (mock, context) -> when(mock.generateJWT()).thenReturn(expectedResponse))) {

            //act
            Object response = userAuthorizerHandler.handleRequest(event, context);
            APIGatewayProxyResponseEvent apiGatewayProxyResponseEvent = (APIGatewayProxyResponseEvent) response;

            //assert
            assertEquals(1, mockedConstruction.constructed().size());
            JWTGeneratorHandler jwtGeneratorHandler = mockedConstruction.constructed().get(0);
            verify(jwtGeneratorHandler).generateJWT();
            assertEquals(Integer.valueOf(HttpStatus.SC_OK), apiGatewayProxyResponseEvent.getStatusCode());
            assertEquals(SUCCESS_MESSAGE, apiGatewayProxyResponseEvent.getBody());
        }
    }

    @Test
    public void testHandleRequestWithJWTGeneratorPath_fail() throws Exception {
        //arrange
        APIGatewayProxyRequestEvent event = new APIGatewayProxyRequestEvent();
        event.setPath(JWT_GENERATOR_API_PATH);

        APIGatewayProxyResponseEvent expectedResponse = new APIGatewayProxyResponseEvent()
            .withStatusCode(500)
            .withBody(ERROR_MESSAGE);

        try (MockedConstruction<JWTGeneratorHandler> mockedConstruction = mockConstruction(JWTGeneratorHandler.class,
            (mock, context) -> when(mock.generateJWT()).thenReturn(expectedResponse))) {

            //act
            Object response = userAuthorizerHandler.handleRequest(event, context);
            APIGatewayProxyResponseEvent apiGatewayProxyResponseEvent = (APIGatewayProxyResponseEvent) response;

            //assert
            assertEquals(1, mockedConstruction.constructed().size());
            JWTGeneratorHandler jwtGeneratorHandler = mockedConstruction.constructed().get(0);
            verify(jwtGeneratorHandler).generateJWT();
            assertEquals(Integer.valueOf(HttpStatus.SC_INTERNAL_SERVER_ERROR), apiGatewayProxyResponseEvent.getStatusCode());
            assertEquals(ERROR_MESSAGE, apiGatewayProxyResponseEvent.getBody());
        }
    }

    @Test
    public void testHandleRequestWithJWTValidatorPath_success() throws Exception {
        //arrange
        APIGatewayProxyRequestEvent event = new APIGatewayProxyRequestEvent();
        event.setPath(JWT_VALIDATOR_API_PATH);
        Map<String, String> header = new HashMap<>();
        String mockToken = "mock-token";

        IamPolicyResponse iamPolicyResponse = createIamPolicyResponse(AUTHORIZED, SUCCESS_MESSAGE);

        try (MockedStatic<JWTUtil> mockedStatic = Mockito.mockStatic(JWTUtil.class);
             MockedConstruction<JWTValidatorHandler> mockedConstruction = mockConstruction(JWTValidatorHandler.class,
                 (mock, context) -> when(mock.validateJWT(event)).thenReturn(iamPolicyResponse))) {

            // Mocking the static method JWTUtil.generateToken()
            mockedStatic.when(JWTUtil::generateToken).thenReturn(mockToken);
            header.put(AUTHORIZATION_HEADER, BEARER_PREFIX + JWTUtil.generateToken());
            event.setHeaders(header);

            //act
            Object result = userAuthorizerHandler.handleRequest(event, context);
            IamPolicyResponse iamPolicyResult = (IamPolicyResponse) result;

            //assert
            assertEquals(1, mockedConstruction.constructed().size());
            JWTValidatorHandler jwtValidatorHandler = mockedConstruction.constructed().get(0);
            verify(jwtValidatorHandler).validateJWT(event);
            assertEquals(SUCCESS_MESSAGE, iamPolicyResult.getContext().get(MESSAGE));
            assertEquals(AUTHORIZED, getEffect(iamPolicyResult));
        }
    }

    @Test
    public void testHandleRequestWithJWTValidatorPath_expiresToken() throws Exception {
        //arrange
        APIGatewayProxyRequestEvent event = new APIGatewayProxyRequestEvent();
        event.setPath(JWT_VALIDATOR_API_PATH);
        Map<String, String> header = new HashMap<>();
        String expiredToken = "expired-token";
        header.put(AUTHORIZATION_HEADER, BEARER_PREFIX + expiredToken);
        event.setHeaders(header);

        IamPolicyResponse iamPolicyResponse = createIamPolicyResponse(DENIED, EXPIRED_MESSAGE);

        try (MockedConstruction<JWTValidatorHandler> mockedConstruction = mockConstruction(JWTValidatorHandler.class,
            (mock, context) -> when(mock.validateJWT(event)).thenReturn(iamPolicyResponse))) {

            //act
            Object result = userAuthorizerHandler.handleRequest(event, context);
            IamPolicyResponse iamPolicyResult = (IamPolicyResponse) result;

            //assert
            assertEquals(1, mockedConstruction.constructed().size());
            JWTValidatorHandler jwtValidatorHandler = mockedConstruction.constructed().get(0);
            verify(jwtValidatorHandler).validateJWT(event);
            assertEquals(EXPIRED_MESSAGE, iamPolicyResult.getContext().get(MESSAGE));
            assertEquals(DENIED, getEffect(iamPolicyResult));
        }
    }

    @Test
    public void handleRequest_authorizationTokenIsNull_shouldFailToValidateJWT() throws Exception {
        // arrange
        APIGatewayProxyRequestEvent event = new APIGatewayProxyRequestEvent();
        event.setPath(JWT_VALIDATOR_API_PATH);
        event.setHeaders(null);

        IamPolicyResponse iamPolicyResponse = createIamPolicyResponse(DENIED, NULL_INVALID_FORMAT_TOKEN_MESSAGE);

        try (MockedConstruction<JWTValidatorHandler> mockedConstruction = mockConstruction(JWTValidatorHandler.class,
            (mock, context) -> when(mock.validateJWT(event)).thenReturn(iamPolicyResponse))) {

            // act
            Object result = userAuthorizerHandler.handleRequest(event, context);
            IamPolicyResponse response = (IamPolicyResponse) result;

            // assert
            assertEquals(1, mockedConstruction.constructed().size());
            JWTValidatorHandler jwtValidatorHandler = mockedConstruction.constructed().get(0);
            verify(jwtValidatorHandler).validateJWT(event);
            assertEquals(NULL_INVALID_FORMAT_TOKEN_MESSAGE, response.getContext().get(MESSAGE));
            assertEquals(DENIED, getEffect(response));
        }
    }

    private IamPolicyResponse createIamPolicyResponse(String effect, String message) {
        IamPolicyResponse iamPolicyResponse = new IamPolicyResponse();
        IamPolicyResponse.PolicyDocument policyDocument = new IamPolicyResponse.PolicyDocument();
        List<IamPolicyResponse.Statement> statementList = new ArrayList<>();
        IamPolicyResponse.Statement statement = new IamPolicyResponse.Statement();
        statement.setEffect(effect);
        statement.setAction(ACTION);
        statement.setResource(List.of(RESOURCE));
        statementList.add(statement);
        policyDocument.setStatement(statementList);
        iamPolicyResponse.setPolicyDocument(policyDocument);
        Map<String, Object> context = new HashMap<>();
        context.put(MESSAGE, message);
        iamPolicyResponse.setContext(context);
        return iamPolicyResponse;
    }

    private String getEffect(IamPolicyResponse response) {
        Map<String, Object>[] statement = (Map<String, Object>[]) response.getPolicyDocument().get("Statement");
        return statement[0].get("Effect").toString();
    }
}
